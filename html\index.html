<html>
    <head>
        <title></title>
        <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css" integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">
        <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
        <style>
            @import url('https://fonts.googleapis.com/css?family=Roboto+Mono&display=swap');

            @font-face {
                font-family: 'vcr-ocd';
                src: url('vcr-ocd.ttf');
            }

            html {
                overflow-x: hidden;
                overflow-y: hidden;
            }
            #helicontainer {
                font-family: 'Roboto Mono';
                display:none;
                position: relative;
                height: 100vh;
                width: 100vw;
                background-color: transparent !important;
                color: #ffffff;
                text-transform: uppercase;
            }
            .vehicleinfo {
                display:none;
            }
            .heli-model {
                position:absolute;
                left: 50%;
                top: 35%;
                transform: translate(-50%, -50%);
                text-shadow:
                -1px -1px 0 #000,
                    1px -1px 0 #000,
                    -1px 1px 0 #000,
                    1px 1px 0 #000;
            }
            .heli-plate {
                position:absolute;
                left: 50%;
                bottom: 30%;
                transform: translate(-50%, -50%);
                text-shadow:
                -1px -1px 0 #000,
                    1px -1px 0 #000,
                    -1px 1px 0 #000,
                    1px 1px 0 #000;
            }
            .heli-street {
                position:absolute;
                left: 70%;
                top: 50.7%;
                transform: translate(-50%, -50%);
                text-shadow:
                -1px -1px 0 #000,
                    1px -1px 0 #000,
                    -1px 1px 0 #000,
                    1px 1px 0 #000;
            }
            .heli-speed {
                position:absolute;
                left: 35%;
                top: 35%;
                text-shadow:
                -1px -1px 0 #000,
                    1px -1px 0 #000,
                    -1px 1px 0 #000,
                    1px 1px 0 #000;
                font-size: 22px;
            }
            .heli-rectangle {
                position:absolute;
                left: 50%;
                top: 50%;
                width: 70px;
                height: 70px;
                background: transparent;
                border: 1px solid #ab0c0c;
                transform: translate(-50%, -50%);
            }
            .scan {
                position:absolute;
                left: 37%;
                bottom: 39%;
                border: 2px solid #cccccc;
                background: rgba(155, 155, 155, 0.0);
                height: 20vh;
                width: 1vw;
            }
            .scanBar {
                background: #cccccc;
            }
            #camcontainer {
                font-family: 'Roboto Mono';
                position: relative;
                height: 100vh;
                background-color: transparent !important;
            }
            #camlabel {
                position: absolute;
                left: 50%;
                bottom: 10%;
                transform: translate(-50%, -50%);
                font-size: 20px;
                color: white;
                background: rgba(0,0,0,0.9);
                padding: 0.3vw;
                text-align:center;
                text-transform: uppercase;
            }
            #camtimelabel {
                position: absolute;
                right: 10%;
                top: 10%;
                font-size: 20px;
                color: white;
                background: rgba(0,0,0,0.9);
                padding: 0.3vw;
            }
            #camdatelabel {
                position: absolute;
                left: 10%;
                top: 10%;
                font-size: 20px;
                color: white;
                background: rgba(0,0,0,0.9);
                padding: 0.3vw;
            }
            #iplabel {
                position: absolute;
                right: 10%;
                bottom: 15%;
                transform: translate(0, -50%);
                font-size: 20px;
                color: white;
                background: rgba(0,0,0,0.9);
                padding: 0.3vw;
            }
            #connectedlabel {
                position: absolute;
                right: 10%;
                bottom: 10%;
                transform: translate(0, -50%);
                font-size: 20px;
                color: white;
                background: rgba(0,0,0,0.9);
                padding: 0.3vw;
            }
            .connected {
                color:#3cc71a!important;
            }
            .disconnect {
                color:#ba1e1e!important;
            }
            #blockscreen {
                position: absolute;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(25,25,25,1.0);
                display: none;
                z-index: -99;
            }
            .fingerprint-container {
                display: none;
                position: relative;
                width: 60vh;
                margin: 0 auto;
                top: 10vh;
            }
            .fingerprint-frame {
                width: 60vh;
            }
            .take-fingerprint {
                position: absolute;
                height: 9.8vh;
                width: 8.2vh;
                background: rgba(250, 250, 250, 0.45);
                opacity: 0.5;
                margin: 0 0 15.1vh 41.1vh;
                border-radius: 0.7vh;
                left: 0;
                right: 0;
                bottom: 15vh;
                transition: 0.1s;
            }
            .take-fingerprint:hover {
                background: rgba(250, 250, 250, 0.65);
            }
            .fingerprint-id {
                position: absolute;
                width: 24vh;
                height: 42.2vh;
                top: 14vh;
                margin: 0 auto;
                left: -115px;
                right: 0;
                text-align: center;
                /* padding-top: 2vh; */
                font-family: 'Roboto Mono';
                font-weight: bold;
            }
            .fingerprint-screen {
                width: 100%;
                height: 100%;
                background-color: #0e0e30;
            }
            .fingerprint-screen > img {
                height: 14vh;
                margin-top: 4vh;
                border: 1px solid gray;
                border-radius: 0.33vh;
            }
            .fingerprint-screen > p {
                color: white;
                font-size: 1.25vh;
                font-weight: lighter;
                text-align: start;
                padding-left: 2.5vh;
            }
        </style>
    </head>
    <body style="background:rgb(0,0,0,0.0)">
        <div id="camcontainer" v-show="camerasOpen">
            <div id="camlabel">{{ cameraLabel }}</div>
            <div id="camdatelabel">{{ dateLabel }}</div>
            <div id="camtimelabel">{{ timeLabel }}</div>
            <div id="iplabel">{{ ipLabel }}</div>
            <div id="connectedlabel" class="connected">{{ connectLabel }}</div>

            <div id="blockscreen"></div>
        </div>
<!--         <div id="databank">
            <div class="databank-container">
                <div class="databank-bg" style="display:none; background: #ffffff; position: absolute; margin: 0 auto; left:0; right: 0; top: 12vh; width:70vw;height:70vh;"></div>
                <iframe src="https://yoursite.com" name="databank" frameborder="0" style="display:none; position: absolute; margin: 0 auto; left:0; right: 0; top: 12vh; width:70vw;height:70vh;"></iframe>
                <img src="tablet-frame.png" alt="tablet" class="tablet-frame" style="display:none; pointer-events: none; position: absolute; margin: 0 auto; left:0; right: 0; top: 2.5vh; width:80vw;height:89vh;" />
            </div>
        </div> -->
        <div id="fingerprint">
            <div class="fingerprint-container">
                <img src="./fingerprint.png" alt="fingerprint" class="fingerprint-frame">
                <div class="fingerprint-id"> </div>
                <div class="take-fingerprint"></div>
            </div>
        </div>
        <div id="helicontainer">
            <div class="vehicleinfo">
                <div class="heli-model">
                    <p>MODEL: COSAGNETTI</p>
                </div>
                <div class="heli-street">
                    <p>Spanish Ave | Lagunas Pl</p>
                </div>
                <div class="heli-plate">
                    <p>PLATE: 01AGB123</p>
                </div>
                <div class="heli-speed">
                    <p>420 KM/U</p>
                </div>
                <div class="heli-rectangle"></div>
            </div>
            <div class="scan">
                <div class="scanBar"></div>
            </div>
        </div>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js" integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49" crossorigin="anonymous"></script>
        <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js" integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy" crossorigin="anonymous"></script>
        <script src="vue.min.js"></script>
        <script src="script.js"></script>
    </body>
</html>
